import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background/flutter_background.dart';
import 'package:geolocator/geolocator.dart' hide AndroidResource;
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

class BackgroundLocationService {
  static final BackgroundLocationService _instance =
      BackgroundLocationService._internal();
  factory BackgroundLocationService() => _instance;
  BackgroundLocationService._internal();

  static const String _taskName = "atualizarLocalizacaoEntregador";
  static const Duration _updateInterval = Duration(minutes: 15);
  static const double _distanciaMinimaMudanca = 50.0;

  bool _isServiceRunning = false;
  Timer? _locationTimer;
  StreamSubscription<Position>? _positionStream;

  /// Inicializa o serviço de localização em background
  Future<bool> inicializarServico(BuildContext context) async {
    try {
      // 1. Verificar e solicitar permissões
      final permissoesOk = await _verificarPermissoes(context);
      if (!permissoesOk) {
        return false;
      }

      // 2. Configurar background execution (Android)
      if (Platform.isAndroid) {
        await _configurarBackgroundAndroid();
      }

      // 3. Inicializar workmanager se não estiver rodando
      if (!_isServiceRunning) {
        await _inicializarWorkmanager();
      }

      return true;
    } catch (e) {
      debugPrint('Erro ao inicializar serviço de background: $e');
      return false;
    }
  }

  /// Verifica e solicita todas as permissões necessárias
  Future<bool> _verificarPermissoes(BuildContext context) async {
    try {
      // Verificar permissão de localização básica
      PermissionStatus locationStatus = await Permission.location.status;
      if (locationStatus.isDenied) {
        locationStatus = await Permission.location.request();
      }

      if (locationStatus.isPermanentlyDenied) {
        // Usar callback para evitar BuildContext async gap
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _mostrarDialogoPermissao(context, 'Localização', 'location');
        });
        return false;
      }

      if (!locationStatus.isGranted) {
        return false;
      }

      // Verificar permissão de localização em background (Android 10+)
      if (Platform.isAndroid) {
        PermissionStatus backgroundLocationStatus =
            await Permission.locationAlways.status;

        if (backgroundLocationStatus.isDenied) {
          // Primeiro explicar ao usuário usando callback
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _mostrarDialogoExplicacaoBackground(context);
          });
          backgroundLocationStatus = await Permission.locationAlways.request();
        }

        if (backgroundLocationStatus.isPermanentlyDenied) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _mostrarDialogoPermissao(
                context, 'Localização em Background', 'locationAlways');
          });
          return false;
        }

        // Para Android 11+, a permissão de background pode não ser concedida automaticamente
        if (!backgroundLocationStatus.isGranted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _mostrarInstrucaoManualBackground(context);
          });
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('Erro ao verificar permissões: $e');
      return false;
    }
  }

  /// Configura o background execution para Android
  Future<void> _configurarBackgroundAndroid() async {
    if (!Platform.isAndroid) return;

    try {
      const androidConfig = FlutterBackgroundAndroidConfig(
        notificationTitle: "Tá Liso - Entregador Ativo",
        notificationText: "Monitorando localização para entregas",
        notificationImportance: AndroidNotificationImportance.normal,
        notificationIcon:
            AndroidResource(name: 'launcher_icon', defType: 'mipmap'),
      );

      bool hasPermissions = await FlutterBackground.hasPermissions;
      if (!hasPermissions) {
        hasPermissions =
            await FlutterBackground.initialize(androidConfig: androidConfig);
      }

      if (hasPermissions && !FlutterBackground.isBackgroundExecutionEnabled) {
        await FlutterBackground.enableBackgroundExecution();
      }
    } catch (e) {
      debugPrint('Erro ao configurar background Android: $e');
    }
  }

  /// Inicializa o workmanager
  Future<void> _inicializarWorkmanager() async {
    try {
      final String? idEntregador = MyAppState.currentUser?.userID;
      if (idEntregador == null || idEntregador.isEmpty) {
        throw Exception('ID do entregador não disponível');
      }

      await Workmanager().registerPeriodicTask(
        "task_atualizar_localizacao_$idEntregador",
        _taskName,
        frequency: _updateInterval,
        initialDelay: const Duration(seconds: 10),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
        inputData: <String, dynamic>{
          'idEntregador': idEntregador,
          'distanciaMinima': _distanciaMinimaMudanca,
        },
      );

      _isServiceRunning = true;
      debugPrint('Workmanager inicializado com sucesso');
    } catch (e) {
      debugPrint('Erro ao inicializar workmanager: $e');
      rethrow;
    }
  }

  /// Para o serviço de background
  Future<void> pararServico() async {
    try {
      final String? idEntregador = MyAppState.currentUser?.userID;
      if (idEntregador != null && idEntregador.isNotEmpty) {
        await Workmanager()
            .cancelByUniqueName("task_atualizar_localizacao_$idEntregador");
      }

      _locationTimer?.cancel();
      _positionStream?.cancel();

      if (Platform.isAndroid &&
          FlutterBackground.isBackgroundExecutionEnabled) {
        await FlutterBackground.disableBackgroundExecution();
      }

      _isServiceRunning = false;
      debugPrint('Serviço de background parado');
    } catch (e) {
      debugPrint('Erro ao parar serviço: $e');
    }
  }

  /// Atualiza a localização manualmente
  Future<void> atualizarLocalizacaoManual() async {
    try {
      final String? idEntregador = MyAppState.currentUser?.userID;
      if (idEntregador == null || idEntregador.isEmpty) return;

      final Position posicao = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );

      await _salvarLocalizacao(idEntregador, posicao);
    } catch (e) {
      debugPrint('Erro ao atualizar localização manual: $e');
    }
  }

  /// Salva a localização no Firestore
  Future<void> _salvarLocalizacao(String idEntregador, Position posicao) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final double? ultimaLat =
          prefs.getDouble('ultimaPosicaoLatitude_$idEntregador');
      final double? ultimaLon =
          prefs.getDouble('ultimaPosicaoLongitude_$idEntregador');

      bool deveAtualizar = false;

      if (ultimaLat == null || ultimaLon == null) {
        deveAtualizar = true;
      } else {
        final double distancia = Geolocator.distanceBetween(
          ultimaLat,
          ultimaLon,
          posicao.latitude,
          posicao.longitude,
        );
        if (distancia >= _distanciaMinimaMudanca) {
          deveAtualizar = true;
        }
      }

      if (deveAtualizar) {
        await FirebaseFirestore.instance
            .collection('delivery_men_status')
            .doc(idEntregador)
            .set({
          'location': {
            'latitude': posicao.latitude,
            'longitude': posicao.longitude,
          },
          'updatedAt': Timestamp.now(),
          'accuracy': posicao.accuracy,
          'speed': posicao.speed,
        }, SetOptions(merge: true));

        await prefs.setDouble(
            'ultimaPosicaoLatitude_$idEntregador', posicao.latitude);
        await prefs.setDouble(
            'ultimaPosicaoLongitude_$idEntregador', posicao.longitude);

        debugPrint(
            'Localização atualizada: ${posicao.latitude}, ${posicao.longitude}');
      }
    } catch (e) {
      debugPrint('Erro ao salvar localização: $e');
    }
  }

  /// Mostra diálogo explicando a necessidade de permissão de background
  Future<void> _mostrarDialogoExplicacaoBackground(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permissão de Localização'),
          content: const Text(
            'Para funcionar corretamente, o app precisa acessar sua localização mesmo quando estiver em segundo plano. '
            'Isso permite que você receba pedidos e mantenha sua posição atualizada para os clientes.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Entendi'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// Mostra instruções para ativar manualmente a permissão de background
  Future<void> _mostrarInstrucaoManualBackground(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Configuração Manual Necessária'),
          content: const Text(
            'Para o funcionamento completo do app, você precisa:\n\n'
            '1. Ir em Configurações do dispositivo\n'
            '2. Localizar "Tá Liso - Entregador" na lista de apps\n'
            '3. Tocar em "Permissões"\n'
            '4. Tocar em "Localização"\n'
            '5. Selecionar "Permitir o tempo todo"\n\n'
            'Isso garante que você receba pedidos mesmo com o app em segundo plano.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Ir para Configurações'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
            TextButton(
              child: const Text('Mais Tarde'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// Mostra diálogo para permissões negadas permanentemente
  Future<void> _mostrarDialogoPermissao(
      BuildContext context, String tipoPermissao, String permissionKey) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Permissão de $tipoPermissao Necessária'),
          content: Text(
            'O app precisa da permissão de $tipoPermissao para funcionar corretamente. '
            'Por favor, ative a permissão nas configurações do dispositivo.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Ir para Configurações'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  bool get isServiceRunning => _isServiceRunning;

  /// Obtém a localização atual do entregador
  Future<Position?> obterLocalizacaoAtual() async {
    try {
      // Verificar se o serviço de localização está habilitado
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Serviço de localização não está habilitado');
        return null;
      }

      // Verificar permissões
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Permissão de localização negada');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('Permissão de localização negada permanentemente');
        return null;
      }

      // Obter localização atual
      final Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );

      debugPrint(
          'Localização obtida: ${position.latitude}, ${position.longitude}');
      return position;
    } catch (e) {
      debugPrint('Erro ao obter localização atual: $e');
      return null;
    }
  }

  /// Obtém a última localização conhecida do SharedPreferences
  Future<Position?> obterUltimaLocalizacaoConhecida() async {
    try {
      final String? idEntregador = MyAppState.currentUser?.userID;
      if (idEntregador == null || idEntregador.isEmpty) return null;

      final prefs = await SharedPreferences.getInstance();
      final double? latitude =
          prefs.getDouble('ultimaPosicaoLatitude_$idEntregador');
      final double? longitude =
          prefs.getDouble('ultimaPosicaoLongitude_$idEntregador');

      if (latitude != null && longitude != null) {
        // Criar um Position mock com os dados salvos
        return Position(
          latitude: latitude,
          longitude: longitude,
          timestamp: DateTime.now(),
          accuracy: 0.0,
          altitude: 0.0,
          altitudeAccuracy: 0.0,
          heading: 0.0,
          headingAccuracy: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
        );
      }

      return null;
    } catch (e) {
      debugPrint('Erro ao obter última localização conhecida: $e');
      return null;
    }
  }
}
