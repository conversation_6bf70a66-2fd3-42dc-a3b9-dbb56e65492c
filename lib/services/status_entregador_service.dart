import 'dart:async';

import 'package:app_settings/app_settings.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/services/background_location_service.dart';
import 'package:flutter/material.dart';
import 'package:workmanager/workmanager.dart';

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();
  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  final List<void Function(bool)> _listeners = [];
  bool _isOnline = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;
  final BackgroundLocationService _backgroundLocationService =
      BackgroundLocationService();

  final double _distanciaMinimaMudanca = 50.0;

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno();

  bool get isOnline => _isOnline;

  Future<void> initialize(BuildContext contexto) async {
    await verificarEconomiaEnergia(contexto);

    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;

    _statusSubscription?.cancel();
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen((doc) {
      final dados = doc.data() as Map<String, dynamic>?;
      final online = dados?['isOnline'] == true;
      _isOnline = online;
      _notificarListeners(online);
    });

    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    _isOnline = dados?['isOnline'] == true;
  }

  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  void dispose() {
    _statusSubscription?.cancel();
    pararMonitoramentoLocalizacao();
    _listeners.clear();
  }

  Future<void> setOnlineStatus(bool online,
      {double? latitude, double? longitude, BuildContext? contexto}) async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;

    final Map<String, dynamic> dados = {
      'entregador_id': id,
      'lastActive': Timestamp.now(),
      'isOnline': online,
      'updatedAt': Timestamp.now(),
    };

    if (latitude != null && longitude != null) {
      dados['location'] = {
        'latitude': latitude,
        'longitude': longitude,
      };
    }

    await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));

    final bool mudancaStatus = _isOnline != online;
    _isOnline = online;
    _notificarListeners(online);

    if (mudancaStatus) {
      if (online) {
        // Inicializar serviço de background melhorado
        if (contexto != null) {
          try {
            final sucesso =
                await _backgroundLocationService.inicializarServico(contexto);
            if (!sucesso) {
              // Fallback para workmanager tradicional
              await _inicializarWorkmanagerTradicional(id);
            }
          } catch (e) {
            // Fallback para workmanager tradicional
            await _inicializarWorkmanagerTradicional(id);
          }
        } else {
          // Sem contexto, usar workmanager tradicional
          await _inicializarWorkmanagerTradicional(id);
        }
      } else {
        // Parar serviços de background
        try {
          await _backgroundLocationService.pararServico();
          await Workmanager()
              .cancelByUniqueName("task_atualizar_localizacao_$id");
        } catch (e) {
          // Erro ao cancelar tarefa
        }
      }
    }
  }

  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }
    final doc = await _colecaoStatus.doc(id).get();
    return doc.data() as Map<String, dynamic>?;
  }

  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      listener(online);
    }
  }

  Future<void> verificarEconomiaEnergia(BuildContext contexto) async {
    final Battery bateria = Battery();
    final bool economiaAtiva = await bateria.isInBatterySaveMode;
    if (economiaAtiva) {
      await showDialog(
        context: contexto,
        barrierDismissible: false,
        builder: (contextoDialogo) {
          return AlertDialog(
            title: const Text('Modo economia de energia'),
            content: const Text(
                'Para o funcionamento correto do app, desative o modo economia de energia nas configurações.'),
            actions: [
              TextButton(
                onPressed: () {
                  AppSettings.openAppSettings(
                      type: AppSettingsType.batteryOptimization,
                      asAnotherTask: true);
                  Navigator.of(contextoDialogo).pop();
                },
                child: const Text('Ir para opções'),
              ),
            ],
          );
        },
      );
    }
  }

  void pararMonitoramentoLocalizacao() {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isNotEmpty) {
      try {
        _backgroundLocationService.pararServico();
        Workmanager().cancelByUniqueName("task_atualizar_localizacao_$id");
      } catch (e) {
        // Erro ao cancelar tarefa
      }
    }
  }

  /// Método fallback para inicializar workmanager tradicional
  Future<void> _inicializarWorkmanagerTradicional(String id) async {
    try {
      await Workmanager().registerPeriodicTask(
        "task_atualizar_localizacao_$id",
        "atualizarLocalizacaoEntregador",
        frequency: const Duration(minutes: 15),
        initialDelay: const Duration(seconds: 10),
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
        inputData: <String, dynamic>{
          'idEntregador': id,
          'distanciaMinima': _distanciaMinimaMudanca,
        },
      );
    } catch (e) {
      // Workmanager não inicializado ou erro ao registrar tarefa
    }
  }

  /// Atualiza localização manualmente
  Future<void> atualizarLocalizacaoManual() async {
    try {
      await _backgroundLocationService.atualizarLocalizacaoManual();
    } catch (e) {
      // Erro ao atualizar localização
    }
  }

  /// Verifica se o serviço de background está rodando
  bool get isBackgroundServiceRunning =>
      _backgroundLocationService.isServiceRunning;
}
