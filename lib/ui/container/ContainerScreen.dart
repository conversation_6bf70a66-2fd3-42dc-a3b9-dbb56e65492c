import 'dart:async';
import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/services/status_entregador_service.dart';
import 'package:emartdriver/ui/auth/AuthScreen.dart';
import 'package:emartdriver/ui/bank_details/bank_details_Screen.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/ordersScreen/OrdersScreen.dart';
import 'package:emartdriver/ui/privacy_policy/privacy_policy.dart';
import 'package:emartdriver/ui/profile/ProfileScreen.dart';
import 'package:emartdriver/ui/termsAndCondition/terms_and_codition.dart';
import 'package:emartdriver/ui/vincular_lojas/vincular_lojas_lojas.dart';
import 'package:emartdriver/ui/vincular_ta_liso/vincular_ta_liso.dart';
import 'package:emartdriver/ui/wallet/WalletBalanceScreen.dart';
import 'package:emartdriver/widgets/GerenciadorMonitoramentoBloqueio.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class ContainerScreen extends StatefulWidget {
  final User user;

  const ContainerScreen({
    Key? key,
    required this.user,
  }) : super(key: key);

  @override
  _ContainerScreen createState() {
    return _ContainerScreen();
  }
}

enum DrawerSelection {
  Home,
  vinculoLojas,
  vinculoTaLiso,
  Cuisines,
  Search,
  Cart,
  Drivers,
  rideSetting,
  Profile,
  Orders,
  Logout,
  Wallet, // Kept for compatibility with other screens
  BankInfo,
  termsCondition,
  privacyPolicy,
  inbox, // Kept for compatibility with other screens
  chooseLanguage, // Kept for compatibility with other screens
}

class _ContainerScreen extends State<ContainerScreen> {
  final fireStoreUtils = FireStoreUtils();
  late Widget _currentWidget;
  DrawerSelection _drawerSelection = DrawerSelection.Home;
  final StatusEntregadorService _onlineStatusService =
      StatusEntregadorService();

  // Referência para o listener para permitir remoção adequada
  Function(bool)? _statusListener;

  bool isLoading = true;
  bool _isOnline = false; // Variável local para controlar o estado da UI
  String _versaoAplicativo = '';
  DateTime pre_backpress = DateTime.now();

  final audioPlayer = AudioPlayer(playerId: "playerId");

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  @override
  Widget build(BuildContext context) {
    return GerenciadorMonitoramentoBloqueio(
      child: WillPopScope(
        onWillPop: () async {
          final timegap = DateTime.now().difference(pre_backpress);
          final cantExit = timegap >= const Duration(seconds: 2);
          pre_backpress = DateTime.now();
          if (cantExit) {
            //show snackbar
            final snack = SnackBar(
              content: Text(
                'Press Back button again to Exit'.tr(),
                style: const TextStyle(color: Colors.white),
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.black,
            );
            ScaffoldMessenger.of(context).showSnackBar(snack);
            return false; // false will do nothing when back press
          } else {
            return true; // true will exit the app
          }
        },
        child: Scaffold(
          drawer: Drawer(
            child: Column(
              children: [
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      DrawerHeader(
                        margin: const EdgeInsets.all(0.0),
                        padding: const EdgeInsets.all(10),
                        decoration: const BoxDecoration(
                          color: Color(0xff425799),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            displayCircleImage(
                                MyAppState.currentUser!.profilePictureURL,
                                60,
                                false),
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                MyAppState.currentUser!.fullName(),
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            Text(
                              MyAppState.currentUser!.email,
                              style: const TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected: _drawerSelection == DrawerSelection.Home,
                          title: const Text('Home').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(() {
                              _drawerSelection = DrawerSelection.Home;
                              // Create a new instance of HomeScreen to ensure it's properly initialized
                              _currentWidget = HomeScreen(
                                  isOnline: _onlineStatusService.isOnline);
                            });

                            // Add a small delay to ensure the widget is built before trying to access it
                            Future.delayed(const Duration(milliseconds: 500),
                                () {
                              // Force a rebuild of the map if needed
                              if (_currentWidget is HomeScreen) {
                                // This will trigger a rebuild of the map
                                setState(() {});
                              }
                            });
                          },
                          leading: const Icon(CupertinoIcons.home),
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected:
                              _drawerSelection == DrawerSelection.vinculoLojas,
                          title: const Text('Vincular lojas').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(() {
                              _drawerSelection = DrawerSelection.vinculoLojas;
                              _currentWidget = const VincularLojasPage();
                            });
                          },
                          leading: const Icon(CupertinoIcons.resize_h),
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected:
                              _drawerSelection == DrawerSelection.vinculoTaLiso,
                          title: const Text('Aderir ao Tá Entregue').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(
                              () {
                                _drawerSelection =
                                    DrawerSelection.vinculoTaLiso;
                                _currentWidget = const VincularTaLiso();
                              },
                            );
                          },
                          leading: const Icon(CupertinoIcons.resize_v),
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected: _drawerSelection == DrawerSelection.Orders,
                          leading: Image.asset(
                            'assets/images/truck.png',
                            color: _drawerSelection == DrawerSelection.Orders
                                ? const Color(0xff425799)
                                : isDarkMode(context)
                                    ? Colors.grey.shade200
                                    : Colors.grey.shade600,
                            width: 24,
                            height: 24,
                          ),
                          title: const Text('Orders').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(() {
                              _drawerSelection = DrawerSelection.Orders;
                              _currentWidget = const OrdersScreen();
                            });
                          },
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected: _drawerSelection == DrawerSelection.Wallet,
                          leading: const Icon(Icons.account_balance_wallet),
                          title: const Text('Carteira').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(() {
                              _drawerSelection = DrawerSelection.Wallet;
                              _currentWidget = const WalletBalanceScreen();
                            });
                          },
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected:
                              _drawerSelection == DrawerSelection.BankInfo,
                          leading: const Icon(Icons.account_balance),
                          title: const Text('Withdraw method').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(() {
                              _drawerSelection = DrawerSelection.BankInfo;
                              _currentWidget = const BankDetailsScreen();
                            });
                          },
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected: _drawerSelection == DrawerSelection.Profile,
                          leading: const Icon(CupertinoIcons.person),
                          title: const Text('Profile').tr(),
                          onTap: () {
                            Navigator.pop(context);
                            setState(() {
                              _drawerSelection = DrawerSelection.Profile;
                              _currentWidget = ProfileScreen(
                                user: MyAppState.currentUser!,
                              );
                            });
                          },
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected: _drawerSelection ==
                              DrawerSelection.termsCondition,
                          leading: const Icon(Icons.policy),
                          title: const Text('Terms and Condition').tr(),
                          onTap: () async {
                            push(context, const TermsAndCondition());
                          },
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected:
                              _drawerSelection == DrawerSelection.privacyPolicy,
                          leading: const Icon(Icons.privacy_tip),
                          title: const Text('Privacy policy').tr(),
                          onTap: () async {
                            push(context, const PrivacyPolicyScreen());
                          },
                        ),
                      ),
                      ListTileTheme(
                        style: ListTileStyle.drawer,
                        selectedColor: const Color(0xff425799),
                        child: ListTile(
                          selected: _drawerSelection == DrawerSelection.Logout,
                          leading: const Icon(Icons.logout),
                          title: const Text('Log out').tr(),
                          onTap: () async {
                            audioPlayer.stop();
                            Navigator.pop(context);
                            await FireStoreUtils.getCurrentUser(
                                    MyAppState.currentUser!.userID)
                                .then((value) {
                              MyAppState.currentUser = value;
                            });
                            MyAppState.currentUser!.isActive = false;
                            MyAppState.currentUser!.lastOnlineTimestamp =
                                Timestamp.now();
                            await FireStoreUtils.updateCurrentUser(
                                MyAppState.currentUser!);
                            await auth.FirebaseAuth.instance.signOut();
                            MyAppState.currentUser = null;
                            pushAndRemoveUntil(
                                context, const AuthScreen(), false);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("V : $_versaoAplicativo"),
                )
              ],
            ),
          ),
          appBar: AppBar(
            iconTheme: IconThemeData(
              color:
                  isDarkMode(context) ? Colors.white : const Color(DARK_COLOR),
            ),
            centerTitle: false,
            backgroundColor:
                isDarkMode(context) ? const Color(DARK_COLOR) : Colors.white,
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 10),
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  color: const Color(0xff425799),
                ),
                child: Image.asset(
                  'assets/images/capacete.png',
                  // color: Color(COLOR_PRIMARY),
                  fit: BoxFit.cover,
                  width: 20,
                  height: 20,
                ),
              ),
              Text(
                "você está ${_isOnline ? "online" : "offline"}",
                style: const TextStyle(
                  color: Color(0xff425799),
                  fontSize: 16,
                ),
              ),
              const SizedBox(width: 20),
              Switch(
                value: _isOnline,
                activeColor: const Color(0xff425799),
                onChanged: (bool value) async {
                  if (_isOnline && !value) {
                    final sair = await showDialog<bool>(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Atenção'),
                          content: const Text(
                              'Você está ficando offline. Deseja fechar o aplicativo?'),
                          actions: [
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(false);
                              },
                              child: const Text('Não, continuar no app'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(true);
                              },
                              child: const Text('Sim, fechar app'),
                            ),
                          ],
                        );
                      },
                    );
                    if (sair == true) {
                      _onlineStatusService.setOnlineStatus(false);
                      Future.delayed(const Duration(milliseconds: 300), () {
                        exit(0);
                      });
                      return;
                    }
                  }
                  await _onlineStatusService.setOnlineStatus(value);
                },
              ),
            ],
          ),
          body: isLoading
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : _currentWidget,
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Remove o listener específico antes do dispose
    if (_statusListener != null) {
      _onlineStatusService.removeListener(_statusListener!);
    }

    // Dispose do service de status online
    _onlineStatusService.dispose();
    super.dispose();
  }

  openBackgroundLocationDialog() {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16.0))),
            contentPadding: const EdgeInsets.only(top: 10.0),
            content: SizedBox(
              //width: 300.0,
              width: MediaQuery.of(context).size.width * 0.6,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
                    child: Text(
                      "Background Location permission".tr(),
                      style: const TextStyle(
                          color: Colors.black, fontWeight: FontWeight.bold),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 8.0, right: 8.0, top: 8.0, bottom: 8.0),
                    child: Text(
                        "This app collects location data to enable location fetching at the time of you are on the way to deliver order or even when the app is in background."
                            .tr()),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.only(top: 20.0, bottom: 20.0),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(16.0),
                            bottomRight: Radius.circular(16.0)),
                      ),
                      child: Text(
                        "Okay".tr(),
                        style: const TextStyle(color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  setCurrency() async {
    /*FireStoreUtils().getCurrency().then((value) => value.forEach((element) {
          if (element.isactive = true) {
            currencyData = element;
          }
        }));*/

    setState(() {
      isLoading = false;
    });
  }

  /// Carrega a versão do aplicativo usando PackageInfo
  Future<void> _carregarVersaoAplicativo() async {
    final informacoesPacote = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        _versaoAplicativo = informacoesPacote.version;
      });
    }
  }

  /// Inicializa todos os componentes do app
  Future<void> _initializeApp() async {
    // Primeiro inicializa o status online
    await _initializeOnlineStatus();

    // Carrega a versão do aplicativo
    await _carregarVersaoAplicativo();

    // Depois carrega as outras configurações
    setCurrency();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      globalContext = context;
    });
    FireStoreUtils.firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  /// Inicializa o service de status online e configura listeners
  Future<void> _initializeOnlineStatus() async {
    debugPrint('ContainerScreen: Initializing online status service...');
    await _onlineStatusService.initialize(
      context,
    );

    setState(() {
      _isOnline = _onlineStatusService.isOnline;
      // Inicializa o widget Home com o status atual
      _currentWidget = HomeScreen(isOnline: _isOnline);
    });

    // Inicializar o serviço de background location com contexto após setState
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final backgroundInitialized = await _onlineStatusService
            .inicializarBackgroundComContexto(context);
        debugPrint(
            'ContainerScreen: Background service initialized: $backgroundInitialized');
      } catch (e) {
        debugPrint(
            'ContainerScreen: Error initializing background service: $e');
      }
    });

    // Cria e adiciona listener para atualizar a UI quando o status mudar
    _statusListener = (isOnline) {
      debugPrint('ContainerScreen: Received status update: $isOnline');
      if (mounted) {
        setState(() {
          _isOnline = isOnline; // Atualiza a variável local
          // Update HomeScreen with new online status if it's currently displayed
          if (_drawerSelection == DrawerSelection.Home) {
            _currentWidget = HomeScreen(isOnline: isOnline);
          }
        });
      }
    };

    _onlineStatusService.addListener(_statusListener!);

    debugPrint(
        'ContainerScreen: Online status service initialized with status: $_isOnline');
  }
}
