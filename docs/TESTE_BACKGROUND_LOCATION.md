# Guia de Teste - Sistema de Localização em Background

## Preparação para Testes

### 1. Build e Instalação
```bash
# Limpar build anterior
flutter clean
flutter pub get

# Build para Android
flutter build apk --debug
# ou
flutter run --debug
```

### 2. Dispositivos Recomendados para Teste
- **Android 10 (API 29)** - Comportamento base
- **Android 11 (API 30)** - Novas restrições de background
- **Android 12+ (API 31+)** - Restrições mais rígidas

## Cenários de Teste

### Teste 1: Primeira Instalação (Permissões)

**Objetivo:** Verificar fluxo de permissões inicial

**Passos:**
1. Instalar app pela primeira vez
2. Fazer login como entregador
3. Tentar ficar online
4. Observar solicitações de permissão

**Resultado Esperado:**
- Solicita permissão de localização básica
- Solicita permissão de background
- Para Android 11+: mostra instruções para configuração manual
- Fallback funciona se permissões negadas

### Teste 2: Funcionamento em Background

**Objetivo:** Verificar se localização atualiza com app em background

**Passos:**
1. Ficar online no app
2. Verificar se notificação aparece
3. Minimizar app (botão home)
4. Aguardar 2-3 minutos
5. Verificar no Firebase se localização foi atualizada

**Resultado Esperado:**
- Notificação "Tá Liso - Rastreando localização" visível
- Localização atualizada no Firestore a cada ~2 minutos
- App continua funcionando em background

### Teste 3: App Fechado Completamente

**Objetivo:** Verificar funcionamento com app fechado

**Passos:**
1. Ficar online no app
2. Fechar app completamente (recent apps → swipe up)
3. Aguardar 5-10 minutos
4. Verificar atualizações no Firebase

**Resultado Esperado:**
- WorkManager continua executando
- Localização ainda é atualizada (menos frequente)
- Serviço sobrevive ao fechamento do app

### Teste 4: Economia de Bateria

**Objetivo:** Verificar comportamento com otimizações ativas

**Passos:**
1. Ativar modo economia de bateria
2. Tentar ficar online
3. Verificar se app detecta restrições
4. Seguir instruções para whitelist

**Resultado Esperado:**
- App detecta modo economia
- Mostra alerta sobre possível impacto
- Oferece instruções para desativar otimizações

### Teste 5: Permissões Revogadas

**Objetivo:** Verificar recuperação quando permissões são removidas

**Passos:**
1. Com app funcionando, ir em Configurações
2. Revogar permissão de localização
3. Voltar ao app
4. Tentar ficar online novamente

**Resultado Esperado:**
- App detecta permissão revogada
- Solicita permissão novamente
- Mostra instruções apropriadas

## Verificações Técnicas

### 1. Logs do Sistema
```bash
# Filtrar logs do app
adb logcat | grep -i "emartdriver\|workmanager\|location"

# Verificar WorkManager
adb logcat | grep -i "workmanager"

# Verificar permissões
adb logcat | grep -i "permission"
```

### 2. Verificar Serviços Ativos
```bash
# Listar serviços em execução
adb shell dumpsys activity services | grep -i "emartdriver"

# Verificar WorkManager jobs
adb shell dumpsys jobscheduler | grep -i "workmanager"
```

### 3. Monitorar Firestore
- Abrir Firebase Console
- Ir em Firestore Database
- Verificar coleção `drivers`
- Observar campo `location` sendo atualizado

### 4. Verificar Notificações
- Notificação deve aparecer quando online
- Deve persistir mesmo com app fechado
- Deve desaparecer quando offline

## Checklist de Funcionalidades

### ✅ Permissões
- [ ] Solicita localização básica
- [ ] Solicita localização em background
- [ ] Mostra instruções para Android 11+
- [ ] Redireciona para configurações quando necessário
- [ ] Detecta permissões revogadas

### ✅ Background Service
- [ ] Inicia quando entregador fica online
- [ ] Mostra notificação persistente
- [ ] Continua funcionando com app minimizado
- [ ] Sobrevive ao fechamento do app
- [ ] Para quando entregador fica offline

### ✅ Atualizações de Localização
- [ ] Atualiza localização no Firestore
- [ ] Respeita distância mínima (50m)
- [ ] Funciona com app em background
- [ ] Funciona com app fechado
- [ ] Frequência adequada (2-5 minutos)

### ✅ Tratamento de Erros
- [ ] Fallback para workmanager tradicional
- [ ] Detecta modo economia de bateria
- [ ] Recupera de falhas de permissão
- [ ] Logs informativos para debug

### ✅ UX/UI
- [ ] Diálogos explicativos claros
- [ ] Instruções passo-a-passo
- [ ] Feedback visual do status
- [ ] Botões para configurações

## Problemas Comuns e Soluções

### Problema: "Localização não atualiza"
**Verificar:**
1. Permissão "Permitir o tempo todo" ativa?
2. App está na whitelist de bateria?
3. Modo economia desativado?
4. Notificação visível?

### Problema: "App para após um tempo"
**Verificar:**
1. Otimizações de bateria desativadas?
2. WorkManager configurado corretamente?
3. Foreground service ativo?

### Problema: "Permissões negadas constantemente"
**Verificar:**
1. Versão do Android (11+ requer configuração manual)
2. Instruções sendo mostradas?
3. Usuário seguindo passos corretamente?

## Métricas de Sucesso

### Performance
- **Frequência de atualização:** 2-5 minutos
- **Precisão:** ±10 metros
- **Consumo de bateria:** <5% por hora
- **Uso de dados:** <1MB por hora

### Confiabilidade
- **Uptime:** >95% quando online
- **Recuperação de falhas:** <30 segundos
- **Compatibilidade:** Android 8+ (API 26+)

### UX
- **Tempo para configurar:** <2 minutos
- **Clareza das instruções:** >90% dos usuários conseguem configurar
- **Feedback visual:** Status sempre visível

## Relatório de Teste

### Template para Documentar Resultados

**Dispositivo:** [Modelo e versão Android]
**Data:** [Data do teste]
**Versão do App:** [Versão testada]

**Testes Realizados:**
- [ ] Primeira instalação
- [ ] Background funcionando
- [ ] App fechado
- [ ] Economia de bateria
- [ ] Permissões revogadas

**Problemas Encontrados:**
1. [Descrição do problema]
   - **Reprodução:** [Como reproduzir]
   - **Impacto:** [Alto/Médio/Baixo]
   - **Solução:** [Proposta de correção]

**Observações:**
[Comentários adicionais sobre o comportamento]

**Status Geral:** [✅ Aprovado / ⚠️ Com ressalvas / ❌ Reprovado]
