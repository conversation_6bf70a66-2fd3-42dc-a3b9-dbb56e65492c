# Configuração de Localização em Background - Tá Liso Entregador

## Visão Geral

Este documento descreve a implementação melhorada do sistema de localização em background para o app Tá Liso - Entregador, garantindo que a localização do entregador seja atualizada mesmo quando o app está em segundo plano.

## Funcionalidades Implementadas

### ✅ 1. Permissões Configuradas

**AndroidManifest.xml:**
- `ACCESS_FINE_LOCATION` - Localização precisa
- `ACCESS_BACKGROUND_LOCATION` - Localização em background
- `FOREGROUND_SERVICE` - Serviços em primeiro plano
- `FOREGROUND_SERVICE_LOCATION` - Serviços de localização (Android 12+)
- `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` - Ignorar otimizações de bateria
- `SYSTEM_ALERT_WINDOW` - Janelas do sistema

### ✅ 2. Serviços Configurados

**Serviço de Foreground:**
```xml
<service
    android:name="com.dexterous.flutterlocalnotifications.ForegroundService"
    android:exported="false"
    android:foregroundServiceType="location" />
```

**Receivers para WorkManager:**
- BatteryChargingProxy
- BatteryNotLowProxy
- StorageNotLowProxy
- NetworkStateProxy

### ✅ 3. Dependências Adicionadas

```yaml
dependencies:
  permission_handler: ^11.3.1
  background_locator_2: ^2.0.6
  flutter_background: ^1.2.0
  workmanager: ^0.7.0
```

## Arquitetura do Sistema

### BackgroundLocationService

Novo serviço principal que gerencia:

1. **Verificação de Permissões:**
   - Solicita permissão de localização básica
   - Solicita permissão de localização em background
   - Mostra diálogos explicativos para o usuário
   - Redireciona para configurações quando necessário

2. **Configuração de Background (Android):**
   - Configura notificação persistente
   - Habilita execução em background
   - Gerencia recursos do sistema

3. **Workmanager Melhorado:**
   - Registra tarefas periódicas
   - Configura constraints apropriadas
   - Fallback para workmanager tradicional

### StatusEntregadorService (Atualizado)

Integração com o novo serviço:

```dart
// Inicializar serviço melhorado
final sucesso = await _backgroundLocationService.inicializarServico(context);
if (!sucesso) {
  // Fallback para workmanager tradicional
  await _inicializarWorkmanagerTradicional(id);
}
```

## Fluxo de Funcionamento

### 1. Inicialização
```
App Inicia → Verifica Permissões → Configura Background → Registra WorkManager
```

### 2. Quando Entregador Fica Online
```
setOnlineStatus(true) → Inicializa Background Service → Mostra Notificação → Inicia Monitoramento
```

### 3. Atualização de Localização
```
Timer/WorkManager → Obtém Localização → Verifica Distância Mínima → Atualiza Firestore
```

### 4. Quando Entregador Fica Offline
```
setOnlineStatus(false) → Para Background Service → Remove Notificação → Cancela Monitoramento
```

## Configurações Específicas por Versão Android

### Android 10 (API 29)
- Permissão `ACCESS_BACKGROUND_LOCATION` solicitada automaticamente
- Usuário pode conceder na primeira solicitação

### Android 11+ (API 30+)
- Permissão `ACCESS_BACKGROUND_LOCATION` requer configuração manual
- App mostra instruções para o usuário ir nas configurações
- Diálogo explicativo sobre a necessidade da permissão

### Android 12+ (API 31+)
- Requer `FOREGROUND_SERVICE_LOCATION`
- Notificação obrigatória para serviços de localização
- Restrições mais rígidas de background

## Tratamento de Erros e Fallbacks

### 1. Permissões Negadas
- Mostra diálogo explicativo
- Redireciona para configurações do app
- Fallback para workmanager tradicional

### 2. Serviço de Background Falha
- Automaticamente usa workmanager tradicional
- Mantém funcionalidade básica
- Log de erros para debugging

### 3. Bateria em Economia
- Detecta modo economia de energia
- Mostra alerta para o usuário
- Sugere desativar otimizações

## Como Usar

### Para Desenvolvedores

1. **Inicializar o serviço:**
```dart
final statusService = StatusEntregadorService();
await statusService.initialize(context);
```

2. **Ativar monitoramento:**
```dart
await statusService.setOnlineStatus(
  true, 
  latitude: lat, 
  longitude: lng, 
  contexto: context
);
```

3. **Verificar status:**
```dart
bool isRunning = statusService.isBackgroundServiceRunning;
```

### Para Usuários

1. **Primeira vez:**
   - App solicita permissão de localização
   - Usuário concede "Permitir enquanto usa o app"
   - App solicita permissão de background
   - Para Android 11+: usuário deve ir em Configurações

2. **Configuração manual (Android 11+):**
   - Configurações → Apps → Tá Liso Entregador
   - Permissões → Localização
   - Selecionar "Permitir o tempo todo"

## Monitoramento e Debug

### Logs Importantes
```dart
debugPrint('Workmanager inicializado com sucesso');
debugPrint('Localização atualizada: $latitude, $longitude');
debugPrint('Serviço de background parado');
```

### Verificações de Status
- `isServiceRunning` - Se o serviço está ativo
- `isBackgroundServiceRunning` - Se o background está funcionando
- Verificação de permissões em tempo real

## Benefícios da Implementação

1. **Compatibilidade:** Funciona em todas as versões do Android
2. **Robustez:** Múltiplos fallbacks garantem funcionamento
3. **UX Melhorada:** Diálogos explicativos e instruções claras
4. **Eficiência:** Otimizações de bateria e recursos
5. **Manutenibilidade:** Código modular e bem documentado

## Próximos Passos

1. **Testes:** Testar em diferentes versões do Android
2. **Otimizações:** Ajustar frequência baseada no uso
3. **Métricas:** Implementar analytics de funcionamento
4. **iOS:** Adaptar para iOS se necessário

## Troubleshooting

### Problema: Localização não atualiza em background
**Solução:** Verificar se permissão "Permitir o tempo todo" está ativa

### Problema: App para de funcionar após um tempo
**Solução:** Verificar otimizações de bateria e whitelist do app

### Problema: Permissões negadas
**Solução:** Usar diálogos explicativos e redirecionar para configurações
